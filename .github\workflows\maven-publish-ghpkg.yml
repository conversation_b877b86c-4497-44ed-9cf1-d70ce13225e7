# This workflow will build a package using <PERSON><PERSON> and then publish it to GitHub packages when a release is created
# For more information see: https://github.com/actions/setup-java/blob/main/docs/advanced-usage.md#apache-maven-with-a-settings-path
# also ref:
#  Publishing packages to GitHub Packages:
#    https://docs.github.com/en/actions/use-cases-and-examples/publishing-packages/publishing-java-packages-with-maven#publishing-packages-to-github-packages
#
#  Upgrade to Automatic token authentication, no need for personal access token anymore
#    ref: https://docs.github.com/en/packages/managing-github-packages-using-github-actions-workflows/publishing-and-installing-a-package-with-github-actions#upgrading-a-workflow-that-accesses-a-registry-using-a-personal-access-token
#

name: Publish to GitHub Packages

run-name: 'Package Publish #${{github.run_number}}'

on:
  release:
    types: [published]
  workflow_dispatch:
  
jobs:
  build:
    runs-on: ubuntu-latest

    permissions:
      packages: write
      contents: read

    steps:
    - name: Checkout Code       
      uses: actions/checkout@v4

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'oracle'
        server-id: 'github-package'

    - name: Publish to GitHub Packages Apache Maven
      run: mvn deploy --batch-mode -Pdeploy-github
      env:
        GITHUB_MAVEN_URL: https://maven.pkg.github.com/${{github.repository}}
        GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}

